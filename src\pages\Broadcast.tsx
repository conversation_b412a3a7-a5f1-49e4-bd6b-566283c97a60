
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Image,
  Video,
  FileText,
  Mic,
  Link,
  Play,
  Plus,
  X,
  Send,
  MessageSquare,
  MessageSquareDiff,
  Users,
  User,
  Building2,
  School,
  Briefcase,
  Calendar,
  Clock,
  Info,
} from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON> } from "@/components/ui/separator";
import { <PERSON><PERSON> } from "@/components/ui/badge";
import { OrganizationHierarchySelector } from "@/components/OrganizationHierarchySelector";
import { organizationHierarchy } from "@/data/organizationData";
import { OrganizationTable } from "@/components/OrganizationTable";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { HierarchicalOrganizationSelector } from "@/components/HierarchicalOrganizationSelector";
import { BroadcastHistoryTable } from "@/components/BroadcastHistoryTable";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export default function Broadcast() {
  // Message type state
  const [messageType, setMessageType] = useState("one-way");

  // Target type states - separate for one-way and two-way
  const [oneWayTargetType, setOneWayTargetType] = useState("user");
  const [twoWayTargetType, setTwoWayTargetType] = useState("user");

  // Current target type based on message type
  const targetType = messageType === "one-way" ? oneWayTargetType : twoWayTargetType;
  const setTargetType = messageType === "one-way" ? setOneWayTargetType : setTwoWayTargetType;

  // Selected target ID
  const [selectedTargetId, setSelectedTargetId] = useState("");

  // Organization hierarchy selection
  const [organizationSelection, setOrganizationSelection] = useState({
    businessUnits: [] as string[],
    departmentGroups: [] as string[],
    departments: [] as string[],
    divisions: [] as string[],
    subDivisions: [] as string[],
    categories: [] as string[],
    grades: [] as string[],
    designations: [] as string[],
  });

  // Organization selection dialog state
  const [organizationDialogOpen, setOrganizationDialogOpen] = useState(false);

  // Message content
  const [messageTitle, setMessageTitle] = useState("");
  const [messageDescription, setMessageDescription] = useState("");
  const [attachmentType, setAttachmentType] = useState("");

  // Two-way specific state
  const [question, setQuestion] = useState({
    text: "",
    options: [
      { text: "", isCorrect: false },
      { text: "", isCorrect: false },
      { text: "", isCorrect: false },
      { text: "", isCorrect: false },
    ],
  });

  // Reset selected target when target type changes
  const handleTargetTypeChange = (type: string) => {
    setTargetType(type);
    setSelectedTargetId("");
  };

  const handleAddOption = () => {
    if (question.options.length < 6) {
      setQuestion({
        ...question,
        options: [...question.options, { text: "", isCorrect: false }],
      });
    }
  };

  const handleRemoveOption = (index: number) => {
    setQuestion({
      ...question,
      options: question.options.filter((_, i) => i !== index),
    });
  };

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...question.options];
    newOptions[index].text = value;
    setQuestion({ ...question, options: newOptions });
  };

  const handleCorrectChange = (index: number) => {
    const newOptions = question.options.map((option, i) => ({
      ...option,
      isCorrect: i === index,
    }));
    setQuestion({ ...question, options: newOptions });
  };

  // Handle unassigning a specific row from the organization selection
  const handleUnassignRow = (rowData: {
    businessUnit: string;
    departmentGroup: string;
    department: string;
    division: string;
    subDivision: string;
    category: string;
    grade: string;
    designation: string;
    assignees?: number;
  }) => {
    setOrganizationSelection(prevSelection => {
      const newSelection = { ...prevSelection };

      // Remove the specific values from each array if they exist
      if (rowData.businessUnit && rowData.businessUnit !== "All Business Units") {
        newSelection.businessUnits = newSelection.businessUnits.filter(bu => bu !== rowData.businessUnit);
      }
      if (rowData.departmentGroup) {
        newSelection.departmentGroups = newSelection.departmentGroups.filter(dg => dg !== rowData.departmentGroup);
      }
      if (rowData.department) {
        newSelection.departments = newSelection.departments.filter(dept => dept !== rowData.department);
      }
      if (rowData.division) {
        newSelection.divisions = newSelection.divisions.filter(div => div !== rowData.division);
      }
      if (rowData.subDivision) {
        newSelection.subDivisions = newSelection.subDivisions.filter(subDiv => subDiv !== rowData.subDivision);
      }
      if (rowData.category) {
        newSelection.categories = newSelection.categories.filter(cat => cat !== rowData.category);
      }
      if (rowData.grade) {
        newSelection.grades = newSelection.grades.filter(grade => grade !== rowData.grade);
      }
      if (rowData.designation) {
        newSelection.designations = newSelection.designations.filter(desig => desig !== rowData.designation);
      }

      return newSelection;
    });

    toast({
      title: "Selection removed",
      description: "The selected organization item has been unassigned.",
    });
  };

  const handleSendMessage = () => {
    // Create a description based on the target type and selection
    let description = "Your message has been sent successfully";

    if (targetType === "user" && selectedTargetId) {
      const userName = {
        "user-1": "Alex Johnson",
        "user-2": "Jamie Smith",
        "user-3": "Taylor Brown",
        "user-4": "Morgan Lee",
        "user-5": "Casey Wilson"
      }[selectedTargetId] || selectedTargetId;

      description += ` to ${userName}.`;
    } else if (targetType === "group" && organizationSelection.businessUnits.length > 0) {
      description += " to the selected organization:";

      if (organizationSelection.businessUnits.length > 0) {
        description += ` ${organizationSelection.businessUnits.length} Business Unit(s)`;
      }

      if (organizationSelection.departmentGroups.length > 0) {
        description += `, ${organizationSelection.departmentGroups.length} Department Group(s)`;
      }

      if (organizationSelection.designations.length > 0) {
        description += `, with ${organizationSelection.designations.length} designation(s)`;
      }
      if (organizationSelection.grades.length > 0) {
        description += `, with ${organizationSelection.grades.length} grade(s)`;
      }
      if (organizationSelection.categories.length > 0) {
        description += `, with ${organizationSelection.categories.length} category(ies)`;
      }
      if (organizationSelection.subDivisions.length > 0) {
        description += `, with ${organizationSelection.subDivisions.length} sub-division(s)`;
      }
      if (organizationSelection.divisions.length > 0) {
        description += `, with ${organizationSelection.divisions.length} division(s)`;
      }
      if (organizationSelection.departments.length > 0) {
        description += `, with ${organizationSelection.departments.length} department(s)`;
      }
    } else if (targetType === "class" && selectedTargetId) {
      const className = {
        "class-1": "Biology 101",
        "class-2": "Computer Science 202",
        "class-3": "Mathematics 303"
      }[selectedTargetId] || selectedTargetId;

      description += ` to ${className}.`;

    } else if (targetType === "all") {
      description += " to all users in the system.";
    } else {
      description += ".";
    }

    toast({
      title: `${messageType === "one-way" ? "One-way" : "Two-way"} message sent`,
      description: description,
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Broadcast</h1>
      </div>

      <Tabs defaultValue="compose">
        <TabsList className="grid w-full grid-cols-2 max-w-md mb-4">
          <TabsTrigger value="compose">Compose</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="compose">
          <Card>
            <CardHeader>
              <CardTitle>Create New Broadcast</CardTitle>
              <CardDescription>
                Send messages to users or groups and track their engagement.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Message Type Selection */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Message Type</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${messageType === "one-way" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                    onClick={() => setMessageType("one-way")}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-full ${messageType === "one-way" ? "bg-primary/10" : "bg-muted"}`}>
                        <MessageSquare
                          className={`h-5 w-5 ${messageType === "one-way" ? "text-primary" : "text-muted-foreground"}`}
                        />
                      </div>
                      <div>
                        <h3 className={`font-medium ${messageType === "one-way" ? "text-primary" : ""}`}>One-Way Broadcast</h3>
                        <p className="text-sm text-muted-foreground mt-1">Send announcements, updates, or information without expecting responses.</p>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${messageType === "two-way" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                    onClick={() => setMessageType("two-way")}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-full ${messageType === "two-way" ? "bg-primary/10" : "bg-muted"}`}>
                        <MessageSquareDiff
                          className={`h-5 w-5 ${messageType === "two-way" ? "text-primary" : "text-muted-foreground"}`}
                        />
                      </div>
                      <div>
                        <h3 className={`font-medium ${messageType === "two-way" ? "text-primary" : ""}`}>Two-Way Interactive</h3>
                        <p className="text-sm text-muted-foreground mt-1">Send messages with questions that recipients can respond to.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Separator */}
              <Separator />

              {/* Target Type Selection - One Way */}
              {messageType === "one-way" && (
                <div className="space-y-3">
                  <Label className="text-base font-medium">Target Audience</Label>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                    <div
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${oneWayTargetType === "user" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                      onClick={() => handleTargetTypeChange("user")}
                    >
                      <div className="flex flex-col items-center justify-center text-center space-y-2">
                        <div className={`p-2 rounded-full ${oneWayTargetType === "user" ? "bg-primary/10" : "bg-muted"}`}>
                          <User className={`h-4 w-4 ${oneWayTargetType === "user" ? "text-primary" : "text-muted-foreground"}`} />
                        </div>
                        <span className={`text-sm font-medium ${oneWayTargetType === "user" ? "text-primary" : ""}`}>Individual</span>
                      </div>
                    </div>

                    <div
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${oneWayTargetType === "group" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                      onClick={() => handleTargetTypeChange("group")}
                    >
                      <div className="flex flex-col items-center justify-center text-center space-y-2">
                        <div className={`p-2 rounded-full ${oneWayTargetType === "group" ? "bg-primary/10" : "bg-muted"}`}>
                          <Users className={`h-4 w-4 ${oneWayTargetType === "group" ? "text-primary" : "text-muted-foreground"}`} />
                        </div>
                        <span className={`text-sm font-medium ${oneWayTargetType === "group" ? "text-primary" : ""}`}>Organization</span>
                      </div>
                    </div>

                    <div
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${oneWayTargetType === "class" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                      onClick={() => handleTargetTypeChange("class")}
                    >
                      <div className="flex flex-col items-center justify-center text-center space-y-2">
                        <div className={`p-2 rounded-full ${oneWayTargetType === "class" ? "bg-primary/10" : "bg-muted"}`}>
                          <School className={`h-4 w-4 ${oneWayTargetType === "class" ? "text-primary" : "text-muted-foreground"}`} />
                        </div>
                        <span className={`text-sm font-medium ${oneWayTargetType === "class" ? "text-primary" : ""}`}>Class</span>
                      </div>
                    </div>

                    <div
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${oneWayTargetType === "all" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                      onClick={() => handleTargetTypeChange("all")}
                    >
                      <div className="flex flex-col items-center justify-center text-center space-y-2">
                        <div className={`p-2 rounded-full ${oneWayTargetType === "all" ? "bg-primary/10" : "bg-muted"}`}>
                          <Briefcase className={`h-4 w-4 ${oneWayTargetType === "all" ? "text-primary" : "text-muted-foreground"}`} />
                        </div>
                        <span className={`text-sm font-medium ${oneWayTargetType === "all" ? "text-primary" : ""}`}>All Users</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Target Type Selection - Two Way */}
              {messageType === "two-way" && (
                <div className="space-y-3">
                  <Label className="text-base font-medium">Target Audience</Label>
                  <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3">
                    <div
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${twoWayTargetType === "user" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                      onClick={() => handleTargetTypeChange("user")}
                    >
                      <div className="flex flex-col items-center justify-center text-center space-y-2">
                        <div className={`p-2 rounded-full ${twoWayTargetType === "user" ? "bg-primary/10" : "bg-muted"}`}>
                          <User className={`h-4 w-4 ${twoWayTargetType === "user" ? "text-primary" : "text-muted-foreground"}`} />
                        </div>
                        <span className={`text-sm font-medium ${twoWayTargetType === "user" ? "text-primary" : ""}`}>Individual</span>
                      </div>
                    </div>

                    <div
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${twoWayTargetType === "group" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                      onClick={() => handleTargetTypeChange("group")}
                    >
                      <div className="flex flex-col items-center justify-center text-center space-y-2">
                        <div className={`p-2 rounded-full ${twoWayTargetType === "group" ? "bg-primary/10" : "bg-muted"}`}>
                          <Users className={`h-4 w-4 ${twoWayTargetType === "group" ? "text-primary" : "text-muted-foreground"}`} />
                        </div>
                        <span className={`text-sm font-medium ${twoWayTargetType === "group" ? "text-primary" : ""}`}>Organization</span>
                      </div>
                    </div>

                    <div
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${twoWayTargetType === "class" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                      onClick={() => handleTargetTypeChange("class")}
                    >
                      <div className="flex flex-col items-center justify-center text-center space-y-2">
                        <div className={`p-2 rounded-full ${twoWayTargetType === "class" ? "bg-primary/10" : "bg-muted"}`}>
                          <School className={`h-4 w-4 ${twoWayTargetType === "class" ? "text-primary" : "text-muted-foreground"}`} />
                        </div>
                        <span className={`text-sm font-medium ${twoWayTargetType === "class" ? "text-primary" : ""}`}>Class</span>
                      </div>
                    </div>

                    <div
                      className={`p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/70 ${twoWayTargetType === "all" ? "border-primary bg-primary/5 shadow-sm" : ""}`}
                      onClick={() => handleTargetTypeChange("all")}
                    >
                      <div className="flex flex-col items-center justify-center text-center space-y-2">
                        <div className={`p-2 rounded-full ${twoWayTargetType === "all" ? "bg-primary/10" : "bg-muted"}`}>
                          <Briefcase className={`h-4 w-4 ${twoWayTargetType === "all" ? "text-primary" : "text-muted-foreground"}`} />
                        </div>
                        <span className={`text-sm font-medium ${twoWayTargetType === "all" ? "text-primary" : ""}`}>All Users</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Separator */}
              <Separator />

              {/* Target Selection */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    {targetType === "user" ? "Select User" :
                     targetType === "group" ? "Select Organization" :
                     targetType === "class" ? "Select Class" : "Select Target"}
                  </Label>

                  {/* Show a badge indicating the current target type */}
                  <Badge variant="outline" className="bg-primary/5 text-primary border-primary/20">
                    {targetType === "user" ? "Individual" :
                     targetType === "group" ? "Organization" :
                     targetType === "class" ? "Class" : "All Users"}
                  </Badge>
                </div>

                {targetType === "user" ? (
                  <div className="border rounded-md p-4 bg-background">
                    <Select onValueChange={setSelectedTargetId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select User" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user-1">Alex Johnson</SelectItem>
                        <SelectItem value="user-2">Jamie Smith</SelectItem>
                        <SelectItem value="user-3">Taylor Brown</SelectItem>
                        <SelectItem value="user-4">Morgan Lee</SelectItem>
                        <SelectItem value="user-5">Casey Wilson</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Show selected user */}
                    {selectedTargetId && (
                      <div className="mt-4 p-3 bg-muted/20 rounded-md">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                            <User className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <div className="font-medium">
                              {selectedTargetId === "user-1" ? "Alex Johnson" :
                               selectedTargetId === "user-2" ? "Jamie Smith" :
                               selectedTargetId === "user-3" ? "Taylor Brown" :
                               selectedTargetId === "user-4" ? "Morgan Lee" :
                               selectedTargetId === "user-5" ? "Casey Wilson" : ""}
                            </div>
                            <div className="text-xs text-muted-foreground">User ID: {selectedTargetId}</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : targetType === "group" ? (
                  <div className="border rounded-md p-4 bg-background">
                    <Button
                      onClick={() => setOrganizationDialogOpen(true)}
                      variant="outline"
                      className="w-full justify-start h-auto py-3 px-4"
                    >
                      <div className="text-left">
                        <p className="text-sm font-medium">Select Organization</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Click to select organizations to target your broadcast
                        </p>
                      </div>
                    </Button>

                    {/* Show selection summary with accordion */}
                    {(organizationSelection.businessUnits.length > 0 ||
                      organizationSelection.departmentGroups.length > 0 ||
                      organizationSelection.departments.length > 0 ||
                      organizationSelection.divisions.length > 0 ||
                      organizationSelection.subDivisions.length > 0 ||
                      organizationSelection.categories.length > 0 ||
                      organizationSelection.grades.length > 0 ||
                      organizationSelection.designations.length > 0) && (
                      <div className="mt-6">
                        <Accordion type="single" collapsible defaultValue="selection-summary" className="border rounded-md">
                          <AccordionItem value="selection-summary" className="border-0">
                            <AccordionTrigger className="px-3 py-2 hover:no-underline hover:bg-gray-100 focus:bg-gray-100" style={{ backgroundColor: "#f9fafb" }}>
                              <div className="flex items-center text-primary">
                                <span className="inline-block w-2 h-2 bg-primary rounded-full mr-2"></span>
                                <h4 className="text-sm font-medium">Selection Summary</h4>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="pt-0 pb-0">
                              <OrganizationTable
                                selection={organizationSelection}
                                onUnassign={handleUnassignRow}
                              />
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      </div>
                    )}

                    {/* Organization Selection Dialog */}
                    <HierarchicalOrganizationSelector
                      open={organizationDialogOpen}
                      onOpenChange={setOrganizationDialogOpen}
                      selection={organizationSelection}
                      onSelectionChange={setOrganizationSelection}
                      onConfirm={() => {}}
                    />
                  </div>
                ) : targetType === "class" ? (
                  <div className="border rounded-md p-4 bg-background">
                    <Select onValueChange={setSelectedTargetId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select Class" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="class-1">Biology 101</SelectItem>
                        <SelectItem value="class-2">Computer Science 202</SelectItem>
                        <SelectItem value="class-3">Mathematics 303</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Show selected class */}
                    {selectedTargetId && (
                      <div className="mt-4 p-3 bg-muted/20 rounded-md">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                            <School className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <div className="font-medium">
                              {selectedTargetId === "class-1" ? "Biology 101" :
                               selectedTargetId === "class-2" ? "Computer Science 202" :
                               selectedTargetId === "class-3" ? "Mathematics 303" : ""}
                            </div>
                            <div className="text-xs text-muted-foreground">Class ID: {selectedTargetId}</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                ) : (
                  <div className="border rounded-md p-4 bg-background">
                    <div className="flex items-center p-3 bg-muted/20 rounded-md text-sm">
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                        <Briefcase className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">All Users</div>
                        <div className="text-xs text-muted-foreground">
                          Your message will be sent to all users in the system.
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Message Content */}
              <div className="space-y-4">
                <div>
                  <Label className="text-base font-medium">Message Content</Label>
                  <div className="mt-3 space-y-4">
                    <div>
                      <Label>Title</Label>
                      <Input
                        placeholder="Enter message title"
                        className="mt-1"
                        value={messageTitle}
                        onChange={(e) => setMessageTitle(e.target.value)}
                      />
                    </div>

                    <div>
                      <Label>Description</Label>
                      <Textarea
                        placeholder="Enter message description"
                        className="mt-1"
                        rows={4}
                        value={messageDescription}
                        onChange={(e) => setMessageDescription(e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* Attachments */}
                <div>
                  <Label>Attachment (Optional)</Label>
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mt-1">
                    {[
                      { value: "image", icon: Image, label: "Image" },
                      { value: "video", icon: Video, label: "Video" },
                      { value: "file", icon: FileText, label: "File" },
                      { value: "audio", icon: Mic, label: "Audio" },
                      { value: "link", icon: Link, label: "Link" },
                      { value: "youtube", icon: Play, label: "YouTube" },
                    ].map((item) => (
                      <div
                        key={item.value}
                        className={`p-2 border rounded-md cursor-pointer flex flex-col items-center justify-center space-y-1 transition-all hover:border-primary/50 ${
                          attachmentType === item.value
                            ? "border-primary bg-primary/5 shadow-sm"
                            : ""
                        }`}
                        onClick={() => setAttachmentType(item.value)}
                      >
                        <item.icon
                          className={`h-4 w-4 ${
                            attachmentType === item.value
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                        <span
                          className={`text-xs ${
                            attachmentType === item.value
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        >
                          {item.label}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Attachment Upload Area */}
                {attachmentType && (
                  <div className="border border-dashed rounded-md p-4 text-center bg-muted/30">
                    <div className="space-y-2">
                      <div className="flex justify-center">
                        {attachmentType === "image" && <Image className="h-8 w-8 text-muted-foreground" />}
                        {attachmentType === "video" && <Video className="h-8 w-8 text-muted-foreground" />}
                        {attachmentType === "file" && <FileText className="h-8 w-8 text-muted-foreground" />}
                        {attachmentType === "audio" && <Mic className="h-8 w-8 text-muted-foreground" />}
                        {attachmentType === "link" && <Link className="h-8 w-8 text-muted-foreground" />}
                        {attachmentType === "youtube" && <Play className="h-8 w-8 text-muted-foreground" />}
                      </div>
                      <div className="text-sm font-medium">
                        {attachmentType === "link" || attachmentType === "youtube" ? (
                          <Input placeholder={`Enter ${attachmentType} URL`} className="max-w-md mx-auto" />
                        ) : (
                          <>
                            <p>Drag and drop or click to upload</p>
                            <Button variant="outline" size="sm" className="mt-2">
                              Upload {attachmentType}
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Two-way Interactive Question */}
                {messageType === "two-way" && (
                  <Card className="mt-6 border-primary/20">
                    <CardHeader>
                      <CardTitle className="text-lg">Interactive Question</CardTitle>
                      <CardDescription>
                        Create a question for recipients to answer. You'll be able to view responses later.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Question Text</Label>
                        <Textarea
                          placeholder="Enter your question"
                          className="mt-1"
                          value={question.text}
                          onChange={(e) =>
                            setQuestion({ ...question, text: e.target.value })
                          }
                        />
                      </div>

                      <div>
                        <div className="flex justify-between items-center">
                          <Label>Options (select one correct answer)</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleAddOption}
                            disabled={question.options.length >= 6}
                            className="h-7 text-xs"
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add Option
                          </Button>
                        </div>
                        <div className="space-y-2 mt-2">
                          {question.options.map((option, index) => (
                            <div
                              key={index}
                              className="flex items-center space-x-2"
                            >
                              <RadioGroup>
                                <div className="flex items-center space-x-2">
                                  <RadioGroupItem
                                    value={`option-${index}`}
                                    id={`option-${index}`}
                                    checked={option.isCorrect}
                                    onClick={() => handleCorrectChange(index)}
                                  />
                                </div>
                              </RadioGroup>
                              <Input
                                placeholder={`Option ${index + 1}`}
                                value={option.text}
                                onChange={(e) =>
                                  handleOptionChange(index, e.target.value)
                                }
                                className="flex-1"
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveOption(index)}
                                disabled={question.options.length <= 2}
                                className="h-8 w-8"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Send Button */}
                <Button
                  className="w-full mt-6"
                  size="lg"
                  onClick={handleSendMessage}
                >
                  <Send className="mr-2 h-4 w-4" />
                  Send {messageType === "one-way" ? "One-Way" : "Two-Way"} Message
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Broadcast History</CardTitle>
              <CardDescription>
                View and manage your previous broadcasts and track engagement metrics.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BroadcastHistoryTable />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
